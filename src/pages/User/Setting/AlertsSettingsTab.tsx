import React, { useState, useEffect, useCallback } from "react";
import {
  FaBell,
  FaCreditCard,
  FaToggleOn,
  FaToggleOff,
  FaLock,
  FaExclamationTriangle,
  FaPercentage,
  FaRegClock,
  FaHistory,
  FaCrown,
  FaSms,
  FaEnvelope,
  FaInfoCircle,
  FaSpinner,
  FaEye,
  FaCheck,
  FaGift,
  FaPlus,
  FaEdit,
  FaTrash,
} from "react-icons/fa";
import {
  alertSettingsApi,
  AlertSettings,
  UpdateAlertSettingsPayload,
  AlertHistoryItem,
  customAlertApi,
  CustomAlert,
  billingApi,
  creditCardApi,
} from "@/services/api";
import { toast } from "react-toastify";

interface AlertToggleProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  isEnabled: boolean;
  onToggle: () => void;
  isPremium?: boolean;
}

const AlertSettingCard: React.FC<AlertToggleProps> = ({
  title,
  description,
  icon,
  isEnabled,
  onToggle,
  isPremium,
}) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-start mb-3">
        <div className="flex items-center">
          <div className="bg-blue-100 text-blue-600 p-2 rounded-full mr-3">
            {icon}
          </div>
          <div>
            <h3 className="text-md font-semibold text-gray-800">{title}</h3>
            <p className="text-xs text-gray-500">{description}</p>
          </div>
        </div>
        <button
          onClick={onToggle}
          className={`text-3xl ${
            isEnabled ? "text-blue-600" : "text-gray-400"
          }`}
          disabled={isPremium}
        >
          {isEnabled ? <FaToggleOn /> : <FaToggleOff />}
        </button>
      </div>
      {isPremium && (
        <p className="text-xs text-purple-600 flex items-center mt-2">
          <FaCrown className="mr-1" /> Premium Feature
        </p>
      )}
    </div>
  );
};

const AlertsSettingsTab: React.FC = () => {
  const [settings, setSettings] = useState<AlertSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isSavingSection, setIsSavingSection] = useState<string | null>(null);
  const [isPremium, setIsPremium] = useState(false);
  const [customAlerts, setCustomAlerts] = useState<CustomAlert[]>([]);
  const [isLoadingCustomAlerts, setIsLoadingCustomAlerts] = useState(false);
  const [showCustomAlertModal, setShowCustomAlertModal] = useState(false);
  const [alertFormData, setAlertFormData] = useState<Partial<CustomAlert>>({
    name: "",
    description: "",
    alert_type: "custom",
    threshold_value: 0,
    is_email_enabled: true,
    is_sms_enabled: false,
  });
  const [alertFormError, setAlertFormError] = useState("");
  const [creditCards, setCreditCards] = useState<any[]>([]);
  const [isSubmittingAlert, setIsSubmittingAlert] = useState(false);

  // New state for alert history
  const [alertHistory, setAlertHistory] = useState<AlertHistoryItem[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [historyPagination, setHistoryPagination] = useState({
    page: 1,
    limit: 10,
    totalCount: 0,
    totalPages: 0,
    hasMore: false,
  });

  const initialLoad = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await alertSettingsApi.getSettings();
      if (response.success && response.settings) {
        setSettings(response.settings);
      } else {
        toast.error("Failed to load alert settings.");
      }
    } catch (error) {
      console.error("Error fetching alert settings:", error);
      toast.error("An error occurred while fetching alert settings.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // New function to fetch alert history
  const fetchAlertHistory = useCallback(async (page = 1) => {
    setIsLoadingHistory(true);
    try {
      const response = await alertSettingsApi.getAlertHistory({
        page,
        limit: 10,
        sort_by: "create_at",
        sort_order: "desc",
      });

      if (response.success) {
        if (page === 1) {
          setAlertHistory(response.alertHistory);
        } else {
          setAlertHistory((prev) => [...prev, ...response.alertHistory]);
        }
        setHistoryPagination(response.pagination);
      } else {
        toast.error("Failed to load alert history.");
      }
    } catch (error) {
      console.error("Error fetching alert history:", error);
      toast.error("An error occurred while fetching alert history.");
    } finally {
      setIsLoadingHistory(false);
    }
  }, []);

  // Function to handle marking alert as read/dismissed
  const handleUpdateAlertStatus = async (
    alertId: number,
    status: string,
    is_read = true
  ) => {
    try {
      const response = await alertSettingsApi.updateAlertHistoryStatus(
        alertId,
        {
          status,
          is_read,
        }
      );

      if (response.success) {
        // Update local state to reflect changes
        setAlertHistory((prev) =>
          prev.map((alert) =>
            alert.id === alertId
              ? { ...alert, status: status, is_read: is_read }
              : alert
          )
        );
        toast.success(
          `Alert ${status === "dismissed" ? "dismissed" : "marked as read"}.`
        );
      } else {
        toast.error(response.message || "Failed to update alert status.");
      }
    } catch (error) {
      console.error("Error updating alert status:", error);
      toast.error("An error occurred while updating alert status.");
    }
  };

  // Check if user has premium subscription
  const checkPremiumStatus = useCallback(async () => {
    try {
      const response = await billingApi.getCurrentSubscription();
      if (response.success && response.subscription) {
        setIsPremium(response.subscription.plan_name === "premium");
      }
    } catch (error) {
      console.error("Error checking premium status:", error);
    }
  }, []);

  // Fetch custom alerts
  const fetchCustomAlerts = useCallback(async () => {
    if (!isPremium) return;

    setIsLoadingCustomAlerts(true);
    try {
      const response = await customAlertApi.getUserAlerts();
      if (response.success) {
        setCustomAlerts(response.alerts || []);
      } else {
        toast.error(response.message || "Failed to load custom alerts");
      }
    } catch (error) {
      console.error("Error fetching custom alerts:", error);
      toast.error("An error occurred while fetching custom alerts");
    } finally {
      setIsLoadingCustomAlerts(false);
    }
  }, [isPremium]);

  // Fetch credit cards for the dropdown
  const fetchCreditCards = useCallback(async () => {
    try {
      const response = await creditCardApi.getCards();
      if (response.success) {
        setCreditCards(response.cards || []);
      }
    } catch (error) {
      console.error("Error fetching credit cards:", error);
    }
  }, []);

  // Create custom alert
  const createCustomAlert = async () => {
    setIsSubmittingAlert(true);
    setAlertFormError("");

    try {
      const response = await customAlertApi.createAlert(alertFormData);

      if (response.success && response.alert) {
        setCustomAlerts([...customAlerts, response.alert]);
        setShowCustomAlertModal(false);
        resetAlertForm();
        toast.success("Custom alert created successfully");
      } else if (response.requiresUpgrade) {
        toast.error("Custom alerts require a premium subscription");
      } else {
        setAlertFormError(response.message || "Failed to create alert");
      }
    } catch (error) {
      console.error("Error creating custom alert:", error);
      setAlertFormError("An error occurred while creating the alert");
    } finally {
      setIsSubmittingAlert(false);
    }
  };

  // Update custom alert
  const updateCustomAlert = async () => {
    if (!alertFormData.id) return;

    setIsSubmittingAlert(true);
    setAlertFormError("");

    try {
      const response = await customAlertApi.updateAlert(
        alertFormData.id,
        alertFormData
      );

      if (response.success && response.alert) {
        setCustomAlerts(
          customAlerts.map((alert) =>
            alert.id === alertFormData.id ? response.alert! : alert
          )
        );
        setShowCustomAlertModal(false);
        resetAlertForm();
        toast.success("Custom alert updated successfully");
      } else {
        setAlertFormError(response.message || "Failed to update alert");
      }
    } catch (error) {
      console.error("Error updating custom alert:", error);
      setAlertFormError("An error occurred while updating the alert");
    } finally {
      setIsSubmittingAlert(false);
    }
  };

  // Delete custom alert
  const deleteCustomAlert = async (alertId: number) => {
    if (!confirm("Are you sure you want to delete this alert?")) return;

    try {
      const response = await customAlertApi.deleteAlert(alertId);

      if (response.success) {
        setCustomAlerts(customAlerts.filter((alert) => alert.id !== alertId));
        toast.success("Custom alert deleted successfully");
      } else {
        toast.error(response.message || "Failed to delete alert");
      }
    } catch (error) {
      console.error("Error deleting custom alert:", error);
      toast.error("An error occurred while deleting the alert");
    }
  };

  // Edit custom alert (open modal with data)
  const editCustomAlert = (alert: CustomAlert) => {
    setAlertFormData({ ...alert });
    setShowCustomAlertModal(true);
  };

  // Toggle notification method
  const toggleAlertMethod = (alert: CustomAlert, method: "email" | "sms") => {
    const field = method === "email" ? "is_email_enabled" : "is_sms_enabled";
    const updatedAlert = { ...alert, [field]: !alert[field] };

    customAlertApi
      .updateAlert(alert.id, { [field]: !alert[field] })
      .then((response) => {
        if (response.success && response.alert) {
          setCustomAlerts(
            customAlerts.map((a) => (a.id === alert.id ? response.alert! : a))
          );
        } else {
          toast.error(
            response.message || `Failed to update ${method} settings`
          );
        }
      })
      .catch((error) => {
        console.error(`Error updating ${method} settings:`, error);
        toast.error(`An error occurred while updating ${method} settings`);
      });
  };

  // Reset alert form
  const resetAlertForm = () => {
    setAlertFormData({
      name: "",
      description: "",
      alert_type: "custom",
      threshold_value: 0,
      is_email_enabled: true,
      is_sms_enabled: false,
    });
    setAlertFormError("");
  };

  // Initialize component
  useEffect(() => {
    initialLoad();
    fetchAlertHistory();
    checkPremiumStatus();
    fetchCreditCards();
  }, [initialLoad, fetchAlertHistory, checkPremiumStatus, fetchCreditCards]);

  // Fetch custom alerts when premium status changes
  useEffect(() => {
    fetchCustomAlerts();
  }, [fetchCustomAlerts, isPremium]);

  const handleUpdateSettings = async (
    updatedValues: Partial<AlertSettings>,
    sectionName?: string
  ) => {
    if (!settings) return;

    if (sectionName) setIsSavingSection(sectionName);
    else setIsSaving(true);

    const previousSettings = { ...settings };
    setSettings((prev) => ({ ...prev!, ...updatedValues }));

    try {
      const response = await alertSettingsApi.updateSettings(
        updatedValues as UpdateAlertSettingsPayload
      );
      if (response.success && response.settings) {
        setSettings(response.settings);
        toast.success(`${sectionName || "Settings"} updated successfully!`);
      } else {
        setSettings(previousSettings);
        toast.error(
          response.message || `Failed to update ${sectionName || "settings"}.`
        );
      }
    } catch (error) {
      setSettings(previousSettings);
      console.error(`Error updating ${sectionName || "settings"}:`, error);
      toast.error(
        `An error occurred while updating ${sectionName || "settings"}.`
      );
    } finally {
      if (sectionName) setIsSavingSection(null);
      else setIsSaving(false);
    }
  };

  const handleToggleAllAlerts = (enable: boolean) => {
    handleUpdateSettings({ all_alerts_enabled: enable }, "All Alerts");
  };

  const handleTogglePaymentReminders = () => {
    if (settings) {
      handleUpdateSettings(
        {
          payment_due_reminders_enabled:
            !settings.payment_due_reminders_enabled,
        },
        "Payment Due Reminders"
      );
    }
  };

  const handleToggleInterestAlerts = () => {
    if (settings) {
      handleUpdateSettings(
        {
          interest_period_alerts_enabled:
            !settings.interest_period_alerts_enabled,
        },
        "0% Interest Period Alerts"
      );
    }
  };

  const handlePaymentSettingsSave = () => {
    if (!settings) return;
    const payload: Partial<AlertSettings> = {
      payment_due_first_reminder_days_before:
        settings.payment_due_first_reminder_days_before,
      payment_due_first_reminder_email:
        settings.payment_due_first_reminder_email,
      payment_due_first_reminder_sms: settings.payment_due_first_reminder_sms,
      payment_due_second_reminder_days_before:
        settings.payment_due_second_reminder_days_before,
      payment_due_second_reminder_email:
        settings.payment_due_second_reminder_email,
      payment_due_second_reminder_sms: settings.payment_due_second_reminder_sms,
      payment_due_third_reminder_days_before:
        settings.payment_due_third_reminder_days_before,
      payment_due_third_reminder_email:
        settings.payment_due_third_reminder_email,
      payment_due_third_reminder_sms: settings.payment_due_third_reminder_sms,
      contact_email: settings.contact_email,
      contact_mobile: settings.contact_mobile,
    };
    handleUpdateSettings(payload, "Payment Due Reminders");
  };

  const handleInterestSettingsSave = () => {
    if (!settings) return;
    const payload: Partial<AlertSettings> = {
      interest_period_first_notice_days_before:
        settings.interest_period_first_notice_days_before,
      interest_period_first_notice_email:
        settings.interest_period_first_notice_email,
      interest_period_first_notice_sms:
        settings.interest_period_first_notice_sms,
      interest_period_second_notice_days_before:
        settings.interest_period_second_notice_days_before,
      interest_period_second_notice_email:
        settings.interest_period_second_notice_email,
      interest_period_second_notice_sms:
        settings.interest_period_second_notice_sms,
      interest_period_final_notice_days_before:
        settings.interest_period_final_notice_days_before,
      interest_period_final_notice_email:
        settings.interest_period_final_notice_email,
      interest_period_final_notice_sms:
        settings.interest_period_final_notice_sms,
    };
    handleUpdateSettings(payload, "0% Interest Period Alerts");
  };

  const handleSettingChange = <K extends keyof AlertSettings>(
    key: K,
    value: AlertSettings[K]
  ) => {
    setSettings((prev) => (prev ? { ...prev, [key]: value } : null));
  };

  const reminderTimeOptions = [
    { value: 1, label: "1 day before due date" },
    { value: 2, label: "2 days before due date" },
    { value: 3, label: "3 days before due date" },
    { value: 7, label: "1 week before due date" },
    { value: 14, label: "2 weeks before due date" },
  ];

  const expiryTimeOptions = [
    { value: 7, label: "7 days before expiry" },
    { value: 14, label: "14 days before expiry" },
    { value: 30, label: "30 days before expiry" },
    { value: 60, label: "60 days before expiry" },
  ];

  const renderReminderSetting = (
    label: string,
    daysBeforeKey: keyof AlertSettings,
    emailKey: keyof AlertSettings,
    smsKey: keyof AlertSettings,
    timeOptions: { value: number; label: string }[],
    isPremiumFeature?: boolean,
    isFree?: boolean
  ) => {
    if (!settings) return null;
    return (
      <div className="mb-6 last:mb-0">
        <div className="flex justify-between items-center mb-2">
          <label className="text-sm text-gray-700">
            {label}{" "}
            {isFree && (
              <span className="ml-1 px-1.5 py-0.5 text-xs font-semibold text-green-700 bg-green-100 rounded-full">
                Free
              </span>
            )}
            {isPremiumFeature && !isPremium && (
              <span className="ml-1 px-1.5 py-0.5 text-xs font-semibold text-purple-700 bg-purple-100 rounded-full inline-flex items-center">
                Premium <FaLock className="ml-1 h-2.5 w-2.5" />
              </span>
            )}
            {isPremiumFeature && isPremium && (
              <span className="ml-1 px-1.5 py-0.5 text-xs font-semibold text-purple-700 bg-purple-100 rounded-full inline-flex items-center">
                Premium <FaCrown className="ml-1 h-2.5 w-2.5" />
              </span>
            )}
          </label>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
          <div>
            <p className="text-xs text-gray-500 mb-1">When to send</p>
            <select
              value={settings[daysBeforeKey] as number}
              onChange={(e) =>
                handleSettingChange(
                  daysBeforeKey,
                  parseInt(
                    e.target.value
                  ) as AlertSettings[typeof daysBeforeKey]
                )
              }
              className={`w-full p-2 border rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 ${
                isPremiumFeature && !isPremium
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "border-gray-300"
              }`}
              disabled={(isPremiumFeature && !isPremium) || isSaving}
            >
              {timeOptions.map((opt) => (
                <option key={opt.value} value={opt.value}>
                  {opt.label}
                </option>
              ))}
            </select>
          </div>
          <div>
            <p className="text-xs text-gray-500 mb-1">Notification method</p>
            <div className="flex space-x-4">
              <label
                className={`flex items-center text-sm ${
                  isPremiumFeature && !isPremium
                    ? "text-gray-400 cursor-not-allowed"
                    : "text-gray-700"
                }`}
              >
                <input
                  type="checkbox"
                  checked={settings[emailKey] as boolean}
                  onChange={(e) =>
                    handleSettingChange(
                      emailKey,
                      e.target.checked as AlertSettings[typeof emailKey]
                    )
                  }
                  className="mr-1.5 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  disabled={(isPremiumFeature && !isPremium) || isSaving}
                />
                Email
              </label>
              <label
                className={`flex items-center text-sm ${
                  (isPremiumFeature && !isPremium) || isFree
                    ? "text-gray-400 cursor-not-allowed"
                    : "text-gray-700"
                }`}
              >
                <input
                  type="checkbox"
                  checked={settings[smsKey] as boolean}
                  onChange={(e) =>
                    handleSettingChange(
                      smsKey,
                      e.target.checked as AlertSettings[typeof smsKey]
                    )
                  }
                  className="mr-1.5 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  disabled={
                    (isPremiumFeature && !isPremium) || isFree || isSaving
                  }
                />
                SMS{" "}
                {!isPremium && (
                  <FaLock className="ml-1 h-2.5 w-2.5 text-gray-400" />
                )}
              </label>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // New function to render alert status badges
  const renderAlertStatusBadge = (status: string, isRead: boolean) => {
    switch (status) {
      case "active":
        return isRead ? (
          <span className="mt-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Read
          </span>
        ) : (
          <span className="mt-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            Active
          </span>
        );
      case "dismissed":
        return (
          <span className="mt-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
            Dismissed
          </span>
        );
      case "expired":
        return (
          <span className="mt-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-700">
            Expired
          </span>
        );
      default:
        return null;
    }
  };

  // Replace the Custom Alerts section with this updated version
  const renderCustomAlertsSection = () => (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center">
          <FaBell className="h-5 w-5 text-blue-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-800">Custom Alerts</h3>
        </div>
        <span className="px-2 py-1 text-xs font-semibold text-purple-700 bg-purple-100 rounded-full">
          Premium Feature
        </span>
      </div>
      <p className="text-sm text-gray-600 mb-6">
        Create personalized alerts for specific spending patterns, balance
        thresholds, or reward milestones.
      </p>

      {isPremium ? (
        <>
          <div className="flex justify-end mb-4">
            <button
              onClick={() => {
                resetAlertForm();
                setShowCustomAlertModal(true);
              }}
              className="flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#16c66c] hover:bg-[#238e4e] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FaPlus className="mr-2" /> Create Custom Alert
            </button>
          </div>

          {isLoadingCustomAlerts ? (
            <div className="flex justify-center items-center py-8">
              <FaSpinner className="animate-spin text-blue-500 text-3xl" />
            </div>
          ) : customAlerts.length > 0 ? (
            <div className="space-y-4">
              {customAlerts.map((alert) => (
                <div
                  key={alert.id}
                  className="bg-white border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {alert.name}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {alert.description}
                      </p>

                      <div className="mt-3 flex items-center space-x-2">
                        <button
                          onClick={() => toggleAlertMethod(alert, "email")}
                          className={`flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                            alert.is_email_enabled
                              ? "bg-blue-100 text-blue-800"
                              : "bg-gray-100 text-gray-600"
                          }`}
                        >
                          <FaEnvelope className="mr-1" /> Email
                        </button>

                        <button
                          onClick={() => toggleAlertMethod(alert, "sms")}
                          className={`flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                            alert.is_sms_enabled
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-600"
                          }`}
                        >
                          <FaSms className="mr-1" /> SMS
                        </button>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={() => editCustomAlert(alert)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <FaEdit className="h-5 w-5" />
                      </button>

                      <button
                        onClick={() => deleteCustomAlert(alert.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <FaTrash className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-10 border border-gray-200 rounded-md">
              <p className="text-gray-500">
                You haven't created any custom alerts yet.
              </p>
              <p className="text-gray-500 mt-1">
                Click the button above to create one.
              </p>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-8 px-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <FaLock className="mx-auto h-10 w-10 text-gray-400 mb-3" />
          <h4 className="text-md font-semibold text-gray-700 mb-1">
            Custom Alerts Unavailable
          </h4>
          <p className="text-sm text-gray-500 mb-4">
            Custom alerts are available only with Premium subscription. Upgrade
            now to create personalized alerts for your financial goals.
          </p>
          <a
            href="/user/setting?tab=billing"
            className="bg-purple-600 text-white px-6 py-2.5 rounded-md text-sm font-medium hover:bg-purple-700 flex items-center justify-center mx-auto"
          >
            <FaCrown className="mr-2" /> Upgrade to Premium
          </a>
        </div>
      )}
    </div>
  );

  // Handle form input changes
  const handleAlertInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === "number") {
      setAlertFormData({ ...alertFormData, [name]: parseFloat(value) });
    } else {
      setAlertFormData({ ...alertFormData, [name]: value });
    }
  };

  // Handle checkbox changes
  const handleAlertCheckboxChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, checked } = e.target;
    setAlertFormData({ ...alertFormData, [name]: checked });
  };

  // Render custom alert form modal
  const renderCustomAlertModal = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
        <h2 className="text-xl font-semibold mb-4">
          {alertFormData.id ? "Edit Alert" : "Create New Alert"}
        </h2>

        {alertFormError && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
            {alertFormError}
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Alert Name
            </label>
            <input
              type="text"
              name="name"
              value={alertFormData.name || ""}
              onChange={handleAlertInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Balance Threshold Alert"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={alertFormData.description || ""}
              onChange={handleAlertInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Notify me when my balance exceeds $3,000"
              rows={3}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Alert Type
            </label>
            <select
              name="alert_type"
              value={alertFormData.alert_type || "custom"}
              onChange={handleAlertInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="spending_threshold">Spending Threshold</option>
              <option value="balance">Balance</option>
              <option value="reward">Reward</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Threshold Value
            </label>
            <input
              type="number"
              name="threshold_value"
              value={alertFormData.threshold_value || ""}
              onChange={handleAlertInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="3000"
              min="0"
              step="0.01"
            />
          </div>

          {alertFormData.alert_type === "spending_threshold" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <input
                type="text"
                name="category"
                value={alertFormData.category || ""}
                onChange={handleAlertInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Dining, Travel, etc."
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Card (Optional)
            </label>
            <select
              name="card_id"
              value={alertFormData.card_id || ""}
              onChange={handleAlertInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Cards</option>
              {creditCards.map((card) => (
                <option key={card.id} value={card.id}>
                  {card.name} (*{card.last_four})
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_email_enabled"
                name="is_email_enabled"
                checked={alertFormData.is_email_enabled || false}
                onChange={handleAlertCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label
                htmlFor="is_email_enabled"
                className="ml-2 block text-sm text-gray-700"
              >
                Email
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_sms_enabled"
                name="is_sms_enabled"
                checked={alertFormData.is_sms_enabled || false}
                onChange={handleAlertCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label
                htmlFor="is_sms_enabled"
                className="ml-2 block text-sm text-gray-700"
              >
                SMS
              </label>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => {
              setShowCustomAlertModal(false);
              resetAlertForm();
            }}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>

          <button
            type="button"
            onClick={alertFormData.id ? updateCustomAlert : createCustomAlert}
            disabled={isSubmittingAlert}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#16c66c] hover:bg-[#238e4e] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSubmittingAlert ? (
              <FaSpinner className="inline-block animate-spin mr-2" />
            ) : alertFormData.id ? (
              "Update Alert"
            ) : (
              "Create Alert"
            )}
          </button>
        </div>
      </div>
    </div>
  );

  if (isLoading || !settings) {
    return (
      <div className="flex justify-center items-center h-64">
        <FaSpinner className="animate-spin text-blue-500 text-4xl" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <div className="flex items-center mb-1">
          <FaBell className="h-6 w-6 text-blue-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">Alerts</h2>
        </div>
        <p className="text-sm text-gray-600">
          Configure your notifications for payment due dates, balance alerts,
          promotional offers, and interest rate changes.
        </p>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 mb-2 sm:mb-0">
            Enable/Disable All Alerts
          </h3>
          <div className="flex space-x-2">
            <button
              onClick={() => handleToggleAllAlerts(true)}
              disabled={isSaving || settings.all_alerts_enabled}
              className={`flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors
                ${
                  settings.all_alerts_enabled
                    ? "bg-green-500 text-white shadow-sm hover:bg-green-600 cursor-default"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
            >
              <FaToggleOn className="mr-2 h-5 w-5" /> Enable All Alerts
            </button>
            <button
              onClick={() => handleToggleAllAlerts(false)}
              disabled={isSaving || !settings.all_alerts_enabled}
              className={`flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors
                ${
                  !settings.all_alerts_enabled
                    ? "bg-gray-700 text-white shadow-sm hover:bg-gray-800 cursor-default"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
            >
              <FaToggleOff className="mr-2 h-5 w-5" /> Disable All Alerts
            </button>
          </div>
        </div>
        <div className="bg-blue-50 p-4 rounded-md flex items-start">
          <FaInfoCircle className="h-5 w-5 text-blue-500 mr-3 flex-shrink-0 mt-0.5" />
          <p className="text-sm text-blue-700">
            Customize which alerts you receive and how you receive them. Some
            alerts are available only with a Premium subscription.{" "}
            {!isPremium && (
              <a
                href="/user/setting?tab=billing"
                className="font-medium underline hover:text-blue-800"
              >
                Upgrade now
              </a>
            )}
          </p>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <FaCreditCard className="h-5 w-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">
              Payment Due Date Reminders
            </h3>
          </div>
          <button
            onClick={handleTogglePaymentReminders}
            disabled={isSaving}
            className={`text-3xl ${
              settings.payment_due_reminders_enabled
                ? "text-blue-600"
                : "text-gray-400"
            }`}
          >
            {settings.payment_due_reminders_enabled ? (
              <FaToggleOn />
            ) : (
              <FaToggleOff />
            )}
          </button>
        </div>
        <p className="text-sm text-gray-600 mb-6">
          Get notified before your credit card payment due dates to avoid late
          fees.
        </p>

        {settings.payment_due_reminders_enabled == true && (
          <div className="space-y-6">
            {renderReminderSetting(
              "First Reminder",
              "payment_due_first_reminder_days_before",
              "payment_due_first_reminder_email",
              "payment_due_first_reminder_sms",
              reminderTimeOptions,
              false,
              true
            )}
            {renderReminderSetting(
              "Second Reminder",
              "payment_due_second_reminder_days_before",
              "payment_due_second_reminder_email",
              "payment_due_second_reminder_sms",
              reminderTimeOptions,
              true,
              false
            )}
            {renderReminderSetting(
              "Third Reminder",
              "payment_due_third_reminder_days_before",
              "payment_due_third_reminder_email",
              "payment_due_third_reminder_sms",
              reminderTimeOptions,
              true,
              false
            )}

            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center mb-3">
                <h4 className="text-md font-medium text-gray-800">
                  Contact Preferences
                </h4>
                <span
                  className={`ml-2 px-2 py-1 text-xs font-semibold rounded-full inline-flex items-center ${
                    isPremium
                      ? "text-purple-700 bg-purple-100"
                      : "text-purple-700 bg-purple-100"
                  }`}
                >
                  Premium{" "}
                  {isPremium ? (
                    <FaCrown className="ml-1 h-2.5 w-2.5" />
                  ) : (
                    <FaLock className="ml-1 h-2.5 w-2.5" />
                  )}
                </span>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm text-gray-500 mb-1">
                    Email Address
                  </label>
                  <div className="flex items-center">
                    <input
                      type="email"
                      value={settings.contact_email || ""}
                      onChange={(e) =>
                        handleSettingChange("contact_email", e.target.value)
                      }
                      className={`w-2/3 p-2 border rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 ${
                        !isPremium
                          ? "bg-gray-100 text-gray-400 cursor-not-allowed border-gray-300"
                          : "bg-white text-gray-700 border-gray-300"
                      }`}
                      disabled={!isPremium || isSaving}
                      placeholder={
                        !isPremium ? "Premium feature - Upgrade to edit" : ""
                      }
                    />
                    {!isPremium && (
                      <FaLock className="ml-2 h-4 w-4 text-gray-400" />
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm text-gray-500 mb-1">
                    Mobile Number
                  </label>
                  <div className="flex items-center">
                    <input
                      type="tel"
                      value={settings.contact_mobile || ""}
                      onChange={(e) =>
                        handleSettingChange("contact_mobile", e.target.value)
                      }
                      className={`w-2/3 p-2 border rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 ${
                        !isPremium
                          ? "bg-gray-100 text-gray-400 cursor-not-allowed border-gray-300"
                          : "bg-white text-gray-700 border-gray-300"
                      }`}
                      disabled={!isPremium || isSaving}
                      placeholder={
                        !isPremium ? "Premium feature - Upgrade to edit" : ""
                      }
                    />
                    {!isPremium && (
                      <FaLock className="ml-2 h-4 w-4 text-gray-400" />
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={initialLoad}
                disabled={isSavingSection === "Payment Due Reminders"}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
              >
                Reset
              </button>
              <button
                onClick={handlePaymentSettingsSave}
                disabled={isSavingSection === "Payment Due Reminders"}
                className="px-4 py-2 text-sm font-medium text-white bg-[#16c66c] hover:bg-[#238e4e] rounded-md flex items-center"
              >
                {isSavingSection === "Payment Due Reminders" ? (
                  <FaSpinner className="animate-spin mr-2" />
                ) : null}
                Save Changes
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <FaPercentage className="h-5 w-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">
              0% Interest Period Alerts
            </h3>
          </div>
          <button
            onClick={handleToggleInterestAlerts}
            disabled={isSaving}
            className={`text-3xl ${
              settings.interest_period_alerts_enabled
                ? "text-blue-600"
                : "text-gray-400"
            }`}
          >
            {settings.interest_period_alerts_enabled ? (
              <FaToggleOn />
            ) : (
              <FaToggleOff />
            )}
          </button>
        </div>
        <p className="text-sm text-gray-600 mb-6">
          Get notified before your 0% APR promotional periods end to avoid
          unexpected interest charges.
        </p>
        {settings.interest_period_alerts_enabled == true && (
          <div className="space-y-6">
            {renderReminderSetting(
              "First Notice",
              "interest_period_first_notice_days_before",
              "interest_period_first_notice_email",
              "interest_period_first_notice_sms",
              expiryTimeOptions,
              true,
              false
            )}
            {renderReminderSetting(
              "Second Notice",
              "interest_period_second_notice_days_before",
              "interest_period_second_notice_email",
              "interest_period_second_notice_sms",
              expiryTimeOptions,
              true,
              false
            )}
            {renderReminderSetting(
              "Final Notice",
              "interest_period_final_notice_days_before",
              "interest_period_final_notice_email",
              "interest_period_final_notice_sms",
              expiryTimeOptions,
              false,
              true
            )}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={initialLoad}
                disabled={isSavingSection === "0% Interest Period Alerts"}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
              >
                Reset
              </button>
              <button
                onClick={handleInterestSettingsSave}
                disabled={isSavingSection === "0% Interest Period Alerts"}
                className="px-4 py-2 text-sm font-medium text-white bg-[#16c66c] hover:bg-[#238e4e] rounded-md flex items-center"
              >
                {isSavingSection === "0% Interest Period Alerts" ? (
                  <FaSpinner className="animate-spin mr-2" />
                ) : null}
                Save Changes
              </button>
            </div>
          </div>
        )}
      </div>

      {renderCustomAlertsSection()}

      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <FaHistory className="h-5 w-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">
              Alert History
            </h3>
          </div>
          {historyPagination.totalCount > 0 && (
            <p className="text-sm text-gray-600">
              Showing {alertHistory.length} of {historyPagination.totalCount}{" "}
              alerts
            </p>
          )}
        </div>
        <p className="text-sm text-gray-600 mb-5">
          Review alerts you've received in the past 30 days.
        </p>

        {isLoadingHistory && alertHistory.length === 0 ? (
          <div className="flex justify-center items-center py-10">
            <FaSpinner className="animate-spin text-blue-500 text-3xl" />
          </div>
        ) : alertHistory.length === 0 ? (
          <div className="text-center py-10 border border-gray-200 rounded-md">
            <p className="text-gray-500">No alert history found.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {alertHistory.map((alert) => (
              <div
                key={alert.id}
                className={`flex items-start p-4 border border-gray-200 rounded-md ${
                  !alert.is_read && alert.status === "active"
                    ? "bg-blue-50"
                    : ""
                }`}
              >
                {alert.alert_type === "interest_period" && (
                  <FaPercentage className="h-5 w-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" />
                )}
                {alert.alert_type === "payment_due" && (
                  <FaRegClock className="h-5 w-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
                )}
                {alert.alert_type === "reward" && (
                  <FaGift className="h-5 w-5 text-purple-500 mr-3 mt-0.5 flex-shrink-0" />
                )}
                {!["interest_period", "payment_due", "reward"].includes(
                  alert.alert_type
                ) && (
                  <FaBell className="h-5 w-5 text-gray-500 mr-3 mt-0.5 flex-shrink-0" />
                )}

                <div className="flex-grow">
                  <div className="flex justify-between items-start">
                    <p className="text-sm font-medium text-gray-800">
                      {alert.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(alert.create_at).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </p>
                  </div>
                  <p className="text-sm text-gray-600 mt-0.5">
                    {alert.message}
                  </p>
                  {renderAlertStatusBadge(alert.status, alert.is_read)}
                </div>

                {/* Action buttons, only shown for active alerts */}
                {alert.status === "active" && (
                  <div className="flex flex-col space-y-1 ml-3">
                    {!alert.is_read && (
                      <button
                        onClick={() =>
                          handleUpdateAlertStatus(alert.id, "active", true)
                        }
                        className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                        title="Mark as read"
                      >
                        <FaEye className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      onClick={() =>
                        handleUpdateAlertStatus(alert.id, "dismissed", true)
                      }
                      className="text-sm text-gray-500 hover:text-gray-700 flex items-center"
                      title="Dismiss"
                    >
                      <FaCheck className="h-4 w-4" />
                    </button>
                  </div>
                )}
              </div>
            ))}

            {/* Load More Button */}
            {historyPagination.hasMore && (
              <div className="text-center pt-4">
                <button
                  onClick={() => fetchAlertHistory(historyPagination.page + 1)}
                  disabled={isLoadingHistory}
                  className="px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-300 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoadingHistory ? (
                    <>
                      <FaSpinner className="inline-block animate-spin mr-2" />
                      Loading...
                    </>
                  ) : (
                    "Load More"
                  )}
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Custom Alert Modal */}
      {showCustomAlertModal && renderCustomAlertModal()}
    </div>
  );
};

export default AlertsSettingsTab;
