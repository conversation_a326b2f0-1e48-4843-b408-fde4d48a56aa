import React from "react";

// SVG Components for bank logos
const ChaseLogo: React.FC<{ className?: string }> = ({ className = "" }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" fill="#2563EB"/>
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" stroke="#E5E7EB"/>
    <path d="M26.125 25H5.875V7H26.125V25Z" stroke="#E5E7EB"/>
    <g clipPath="url(#clip0_247_12785)">
      <path d="M22.402 15.1316C22.402 15.1316 22.6691 16.4395 22.7289 16.7137H21.5547C21.6707 16.4008 22.1172 15.1844 22.1172 15.1844C22.1102 15.1949 22.2332 14.8645 22.3035 14.6605L22.402 15.1316ZM26.125 9.8125V22.1875C26.125 23.1191 25.3691 23.875 24.4375 23.875H7.5625C6.63086 23.875 5.875 23.1191 5.875 22.1875V9.8125C5.875 8.88086 6.63086 8.125 7.5625 8.125H24.4375C25.3691 8.125 26.125 8.88086 26.125 9.8125ZM11.2363 18.6438L13.4582 13.1875H11.9641L10.5824 16.9141L10.4313 16.1582L9.93906 13.648C9.8582 13.3 9.60859 13.2016 9.29922 13.1875H7.02461L7 13.2965C7.55547 13.4371 8.05117 13.641 8.48359 13.8977L9.74219 18.6438H11.2363ZM14.5551 18.6508L15.441 13.1875H14.0277L13.1453 18.6508H14.5551ZM19.4734 16.8648C19.4805 16.2426 19.1008 15.768 18.2887 15.3777C17.793 15.1281 17.4906 14.9594 17.4906 14.7027C17.4977 14.4707 17.7473 14.2316 18.3027 14.2316C18.7633 14.2211 19.1008 14.3301 19.3539 14.4391L19.4805 14.4988L19.6738 13.3176C19.3961 13.2086 18.9531 13.0855 18.4082 13.0855C17.0125 13.0855 16.0316 13.8309 16.0246 14.8926C16.0141 15.6766 16.7277 16.1125 17.2621 16.3762C17.807 16.6434 17.9934 16.8191 17.9934 17.0547C17.9863 17.4203 17.5504 17.5891 17.1461 17.5891C16.5836 17.5891 16.2812 17.5012 15.8207 17.2973L15.6344 17.2094L15.4375 18.4363C15.768 18.5875 16.3797 18.7211 17.0125 18.7281C18.4961 18.7316 19.4629 17.9969 19.4734 16.8648ZM24.4375 18.6508L23.2984 13.1875H22.2051C21.8676 13.1875 21.6109 13.2859 21.4668 13.641L19.368 18.6508H20.8516C20.8516 18.6508 21.0941 17.9758 21.1469 17.8316H22.9609C23.0031 18.025 23.1297 18.6508 23.1297 18.6508H24.4375Z" fill="white"/>
    </g>
    <defs>
      <clipPath id="clip0_247_12785">
        <path d="M5.875 7H26.125V25H5.875V7Z" fill="white"/>
      </clipPath>
    </defs>
  </svg>
);

const AmexLogo: React.FC<{ className?: string }> = ({ className = "" }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" fill="#7C3AED"/>
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" stroke="#E5E7EB"/>
    <path d="M26.125 25H5.875V7H26.125V25Z" stroke="#E5E7EB"/>
    <g clipPath="url(#clip0_247_12210)">
      <path d="M7.5625 23.875C6.63051 23.875 5.875 23.1191 5.875 22.1875V9.8125C5.875 8.88051 6.63051 8.125 7.5625 8.125H24.4375C25.3691 8.125 26.125 8.88051 26.125 9.8125V9.89793H23.4707L22.873 11.5703L22.2789 9.89793H18.8617V12.1188L17.8738 9.89793H15.1105L12.2383 16.3902H14.5516V22.1488H21.7129L22.8344 20.9148L23.9559 22.1488H26.125V22.1875C26.125 23.1191 25.3691 23.875 24.4375 23.875H7.5625ZM22.8414 19.7969L21.3578 21.4246H19.6035L21.9766 18.9039L19.6035 16.3551H21.4105L22.8695 17.9969L24.3461 16.3551H26.125L23.7344 18.8898L26.125 21.4246H24.318L22.8414 19.7969ZM26.125 17.4379V20.3664L24.7434 18.8934L26.125 17.4379ZM16.6891 20.2574H19.607V21.4246H15.318V16.3902H19.607V17.5539H16.6891V18.3414H19.5332V19.477H16.6891V20.2609V20.2574ZM24.7645 12.1223L23.4672 15.659H22.2578L20.9641 12.1328V15.659H19.6035V10.6211H21.7551L22.8766 13.7605L24.0086 10.6211H26.125V15.659H24.7645V12.1223ZM17.6348 14.65H15.318L14.8996 15.659H13.3879L15.5816 10.6211H17.3816L19.607 15.659H18.0566L17.6348 14.65ZM16.4746 11.8691L15.7891 13.518H17.1566L16.4746 11.8691Z" fill="white"/>
    </g>
    <defs>
      <clipPath id="clip0_247_12210">
        <path d="M5.875 7H26.125V25H5.875V7Z" fill="white"/>
      </clipPath>
    </defs>
  </svg>
);

// Bank icon configuration interface
interface BankIconConfig {
  icon: React.ReactNode;
  backgroundColor?: string;
  textColor?: string;
  shortName: string;
  hasSvg: boolean;
}

// Bank icon mapping based on issuer names from API and ManualAddCardModal
const bankIconMap: Record<string, BankIconConfig> = {
  // Chase - Has SVG logo
  chase: {
    icon: <ChaseLogo />,
    shortName: "CHASE",
    hasSvg: true,
  },

  // American Express - Text fallback for now
  amex: {
    icon: "AMEX",
    backgroundColor: "bg-blue-500",
    textColor: "text-white",
    shortName: "AMEX",
    hasSvg: false,
  },

  // Citibank - Text fallback for now
  citi: {
    icon: "CITI",
    backgroundColor: "bg-red-600",
    textColor: "text-white",
    shortName: "CITI",
    hasSvg: false,
  },

  // Bank of America - Text fallback for now
  boa: {
    icon: "BOA",
    backgroundColor: "bg-red-500",
    textColor: "text-white",
    shortName: "BOA",
    hasSvg: false,
  },

  // Default fallback
  default: {
    icon:  <ChaseLogo />,
    backgroundColor: "bg-gray-500",
    textColor: "text-white",
    shortName: "BANK",
    hasSvg: false,
  },
};

/**
 * Get bank icon configuration based on issuer name
 * @param issuer - The issuer name from the API (e.g., "Amex", "Chase", "Citi", "BoA")
 * @returns BankIconConfig object with icon, colors, and short name
 */
export const getBankIcon = (issuer: string): BankIconConfig => {
  if (!issuer) return bankIconMap.default;

  const normalizedIssuer = issuer.toLowerCase().trim();

  // Handle various possible issuer name formats
  if (normalizedIssuer.includes("chase")) {
    return bankIconMap.chase;
  }

  if (normalizedIssuer.includes("amex") || normalizedIssuer.includes("american express")) {
    return bankIconMap.amex;
  }

  if (normalizedIssuer.includes("citi") || normalizedIssuer.includes("citibank")) {
    return bankIconMap.citi;
  }

  if (normalizedIssuer.includes("boa") || normalizedIssuer.includes("bank of america")) {
    return bankIconMap.boa;
  }

  // Return default if no match found
  return bankIconMap.default;
};

/**
 * Bank Icon Component
 * @param issuer - The issuer name from the API
 * @param size - Size class for the icon (default: "w-8 h-8")
 * @param className - Additional CSS classes
 */
interface BankIconProps {
  issuer: string;
  size?: string;
  className?: string;
}

export const BankIcon: React.FC<BankIconProps> = ({
  issuer,
  size = "w-8 h-8",
  className = ""
}) => {
  const iconConfig = getBankIcon(issuer);

  // If it's an SVG logo, render it directly
  if (iconConfig.hasSvg) {
    return (
      <div className={`${size} ${className}`}>
        {iconConfig.icon}
      </div>
    );
  }

  // Otherwise, render text-based icon
  return (
    <div
      className={`${size} ${iconConfig.backgroundColor} ${iconConfig.textColor} rounded-md flex items-center justify-center font-bold text-xs shadow-sm border border-gray-200 ${className}`}
    >
      {iconConfig.icon}
    </div>
  );
};

export default BankIcon;
