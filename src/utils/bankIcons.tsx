import React from "react";

// Bank icon configuration interface
interface BankIconConfig {
  icon: React.ReactNode;
  backgroundColor: string;
  textColor: string;
  shortName: string;
}

// Bank icon mapping based on issuer names from API and ManualAddCardModal
const bankIconMap: Record<string, BankIconConfig> = {
  // Chase - Blue brand color
  chase: {
    icon: "CHASE",
    backgroundColor: "bg-blue-600",
    textColor: "text-white",
    shortName: "CHASE",
  },

  // American Express - Blue brand color
  amex: {
    icon: "AMEX",
    backgroundColor: "bg-blue-500",
    textColor: "text-white",
    shortName: "AMEX",
  },

  // Citibank - Red brand color
  citi: {
    icon: "CITI",
    backgroundColor: "bg-red-600",
    textColor: "text-white",
    shortName: "CITI",
  },

  // Bank of America - Red brand color
  boa: {
    icon: "BOA",
    backgroundColor: "bg-red-500",
    textColor: "text-white",
    shortName: "BOA",
  },

  // Default fallback
  default: {
    icon: "BANK",
    backgroundColor: "bg-gray-500",
    textColor: "text-white",
    shortName: "BANK",
  },
};

/**
 * Get bank icon configuration based on issuer name
 * @param issuer - The issuer name from the API (e.g., "Amex", "Chase", "Citi", "BoA")
 * @returns BankIconConfig object with icon, colors, and short name
 */
export const getBankIcon = (issuer: string): BankIconConfig => {
  if (!issuer) return bankIconMap.default;

  const normalizedIssuer = issuer.toLowerCase().trim();

  // Handle various possible issuer name formats
  if (normalizedIssuer.includes("chase")) {
    return bankIconMap.chase;
  }

  if (normalizedIssuer.includes("amex") || normalizedIssuer.includes("american express")) {
    return bankIconMap.amex;
  }

  if (normalizedIssuer.includes("citi") || normalizedIssuer.includes("citibank")) {
    return bankIconMap.citi;
  }

  if (normalizedIssuer.includes("boa") || normalizedIssuer.includes("bank of america")) {
    return bankIconMap.boa;
  }

  // Return default if no match found
  return bankIconMap.default;
};

/**
 * Bank Icon Component
 * @param issuer - The issuer name from the API
 * @param size - Size class for the icon (default: "w-8 h-8")
 * @param className - Additional CSS classes
 */
interface BankIconProps {
  issuer: string;
  size?: string;
  className?: string;
}

export const BankIcon: React.FC<BankIconProps> = ({
  issuer,
  size = "w-8 h-8",
  className = ""
}) => {
  const iconConfig = getBankIcon(issuer);

  return (
    <div
      className={`${size} ${iconConfig.backgroundColor} ${iconConfig.textColor} rounded-md flex items-center justify-center font-bold text-xs shadow-sm border border-gray-200 ${className}`}
    >
      {iconConfig.icon}
    </div>
  );
};

export default BankIcon;
