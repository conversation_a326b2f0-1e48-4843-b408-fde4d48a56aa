import React from "react";

// SVG Components for bank logos
const ChaseLogo: React.FC<{ className?: string }> = ({ className = "" }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" fill="#2563EB"/>
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" stroke="#E5E7EB"/>
    <path d="M26.125 25H5.875V7H26.125V25Z" stroke="#E5E7EB"/>
    <g clipPath="url(#clip0_247_12785)">
      <path d="M22.402 15.1316C22.402 15.1316 22.6691 16.4395 22.7289 16.7137H21.5547C21.6707 16.4008 22.1172 15.1844 22.1172 15.1844C22.1102 15.1949 22.2332 14.8645 22.3035 14.6605L22.402 15.1316ZM26.125 9.8125V22.1875C26.125 23.1191 25.3691 23.875 24.4375 23.875H7.5625C6.63086 23.875 5.875 23.1191 5.875 22.1875V9.8125C5.875 8.88086 6.63086 8.125 7.5625 8.125H24.4375C25.3691 8.125 26.125 8.88086 26.125 9.8125ZM11.2363 18.6438L13.4582 13.1875H11.9641L10.5824 16.9141L10.4313 16.1582L9.93906 13.648C9.8582 13.3 9.60859 13.2016 9.29922 13.1875H7.02461L7 13.2965C7.55547 13.4371 8.05117 13.641 8.48359 13.8977L9.74219 18.6438H11.2363ZM14.5551 18.6508L15.441 13.1875H14.0277L13.1453 18.6508H14.5551ZM19.4734 16.8648C19.4805 16.2426 19.1008 15.768 18.2887 15.3777C17.793 15.1281 17.4906 14.9594 17.4906 14.7027C17.4977 14.4707 17.7473 14.2316 18.3027 14.2316C18.7633 14.2211 19.1008 14.3301 19.3539 14.4391L19.4805 14.4988L19.6738 13.3176C19.3961 13.2086 18.9531 13.0855 18.4082 13.0855C17.0125 13.0855 16.0316 13.8309 16.0246 14.8926C16.0141 15.6766 16.7277 16.1125 17.2621 16.3762C17.807 16.6434 17.9934 16.8191 17.9934 17.0547C17.9863 17.4203 17.5504 17.5891 17.1461 17.5891C16.5836 17.5891 16.2812 17.5012 15.8207 17.2973L15.6344 17.2094L15.4375 18.4363C15.768 18.5875 16.3797 18.7211 17.0125 18.7281C18.4961 18.7316 19.4629 17.9969 19.4734 16.8648ZM24.4375 18.6508L23.2984 13.1875H22.2051C21.8676 13.1875 21.6109 13.2859 21.4668 13.641L19.368 18.6508H20.8516C20.8516 18.6508 21.0941 17.9758 21.1469 17.8316H22.9609C23.0031 18.025 23.1297 18.6508 23.1297 18.6508H24.4375Z" fill="white"/>
    </g>
    <defs>
      <clipPath id="clip0_247_12785">
        <path d="M5.875 7H26.125V25H5.875V7Z" fill="white"/>
      </clipPath>
    </defs>
  </svg>
);

const AmexLogo: React.FC<{ className?: string }> = ({ className = "" }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" fill="#7C3AED"/>
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" stroke="#E5E7EB"/>
    <path d="M26.125 25H5.875V7H26.125V25Z" stroke="#E5E7EB"/>
    <g clipPath="url(#clip0_247_12210)">
      <path d="M7.5625 23.875C6.63051 23.875 5.875 23.1191 5.875 22.1875V9.8125C5.875 8.88051 6.63051 8.125 7.5625 8.125H24.4375C25.3691 8.125 26.125 8.88051 26.125 9.8125V9.89793H23.4707L22.873 11.5703L22.2789 9.89793H18.8617V12.1188L17.8738 9.89793H15.1105L12.2383 16.3902H14.5516V22.1488H21.7129L22.8344 20.9148L23.9559 22.1488H26.125V22.1875C26.125 23.1191 25.3691 23.875 24.4375 23.875H7.5625ZM22.8414 19.7969L21.3578 21.4246H19.6035L21.9766 18.9039L19.6035 16.3551H21.4105L22.8695 17.9969L24.3461 16.3551H26.125L23.7344 18.8898L26.125 21.4246H24.318L22.8414 19.7969ZM26.125 17.4379V20.3664L24.7434 18.8934L26.125 17.4379ZM16.6891 20.2574H19.607V21.4246H15.318V16.3902H19.607V17.5539H16.6891V18.3414H19.5332V19.477H16.6891V20.2609V20.2574ZM24.7645 12.1223L23.4672 15.659H22.2578L20.9641 12.1328V15.659H19.6035V10.6211H21.7551L22.8766 13.7605L24.0086 10.6211H26.125V15.659H24.7645V12.1223ZM17.6348 14.65H15.318L14.8996 15.659H13.3879L15.5816 10.6211H17.3816L19.607 15.659H18.0566L17.6348 14.65ZM16.4746 11.8691L15.7891 13.518H17.1566L16.4746 11.8691Z" fill="white"/>
    </g>
    <defs>
      <clipPath id="clip0_247_12210">
        <path d="M5.875 7H26.125V25H5.875V7Z" fill="white"/>
      </clipPath>
    </defs>
  </svg>
);

const MastercardLogo: React.FC<{ className?: string }> = ({ className = "" }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" fill="#D97706"/>
    <path d="M24 0C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8C0 3.58172 3.58172 0 8 0H24Z" stroke="#E5E7EB"/>
    <path d="M26.125 25H5.875V7H26.125V25Z" stroke="#E5E7EB"/>
    <g clipPath="url(#clip0_247_12141)">
      <path d="M22.852 21.4246C22.852 21.6637 22.6902 21.8359 22.4582 21.8359C22.2191 21.8359 22.0645 21.6531 22.0645 21.4246C22.0645 21.1961 22.2191 21.0133 22.4582 21.0133C22.6902 21.0133 22.852 21.1961 22.852 21.4246ZM11.9254 21.0133C11.6758 21.0133 11.5316 21.1961 11.5316 21.4246C11.5316 21.6531 11.6758 21.8359 11.9254 21.8359C12.1539 21.8359 12.3086 21.6637 12.3086 21.4246C12.3051 21.1961 12.1539 21.0133 11.9254 21.0133ZM16.0563 21.0027C15.8664 21.0027 15.7504 21.1258 15.7223 21.3086H16.3938C16.3621 21.1082 16.2391 21.0027 16.0563 21.0027ZM19.8461 21.0133C19.607 21.0133 19.4629 21.1961 19.4629 21.4246C19.4629 21.6531 19.607 21.8359 19.8461 21.8359C20.0852 21.8359 20.2398 21.6637 20.2398 21.4246C20.2398 21.1961 20.0852 21.0133 19.8461 21.0133ZM23.5691 21.9309C23.5691 21.9414 23.5797 21.9484 23.5797 21.9695C23.5797 21.9801 23.5691 21.9871 23.5691 22.0082C23.5586 22.0188 23.5586 22.0258 23.5516 22.0363C23.541 22.0469 23.534 22.0539 23.5129 22.0539C23.5023 22.0645 23.4953 22.0645 23.4742 22.0645C23.4637 22.0645 23.4566 22.0645 23.4355 22.0539C23.425 22.0539 23.418 22.0434 23.4074 22.0363C23.3969 22.0258 23.3898 22.0188 23.3898 22.0082C23.3793 21.9906 23.3793 21.9801 23.3793 21.9695C23.3793 21.952 23.3793 21.9414 23.3898 21.9309C23.3898 21.9133 23.4004 21.9027 23.4074 21.8922C23.418 21.8816 23.425 21.8816 23.4355 21.8746C23.4531 21.8641 23.4637 21.8641 23.4742 21.8641C23.4918 21.8641 23.5023 21.8641 23.5129 21.8746C23.5305 21.8852 23.541 21.8852 23.5516 21.8922C23.5621 21.8992 23.5586 21.9133 23.5691 21.9309ZM23.4918 21.9801C23.5094 21.9801 23.5094 21.9695 23.5199 21.9695C23.5305 21.959 23.5305 21.952 23.5305 21.9414C23.5305 21.9309 23.5305 21.9238 23.5199 21.9133C23.5094 21.9133 23.5023 21.9027 23.4812 21.9027H23.425V22.0258H23.4531V21.9766H23.4637L23.5023 22.0258H23.5305L23.4918 21.9801ZM26.125 9.84766V22.2227C26.125 23.1543 25.3691 23.9102 24.4375 23.9102H7.5625C6.63086 23.9102 5.875 23.1543 5.875 22.2227V9.84766C5.875 8.91602 6.63086 8.16016 7.5625 8.16016H24.4375C25.3691 8.16016 26.125 8.91602 26.125 9.84766ZM8.125 14.7555C8.125 17.4449 10.3082 19.6246 12.9941 19.6246C13.9504 19.6246 14.8891 19.3363 15.6836 18.8125C13.1207 16.7277 13.1383 12.7937 15.6836 10.709C14.8891 10.1816 13.9504 9.89688 12.9941 9.89688C10.3082 9.89336 8.125 12.0766 8.125 14.7555ZM16 18.5805C18.4785 16.6469 18.468 12.8781 16 10.934C13.532 12.8781 13.5215 16.6504 16 18.5805ZM10.9973 21.2629C10.9973 20.957 10.7969 20.7566 10.4805 20.7461C10.3187 20.7461 10.1465 20.7953 10.0305 20.9746C9.94609 20.8305 9.80195 20.7461 9.60156 20.7461C9.46797 20.7461 9.33438 20.7953 9.22891 20.9359V20.7812H8.94062V22.0715H9.22891C9.22891 21.407 9.14102 21.0098 9.54531 21.0098C9.90391 21.0098 9.83359 21.3684 9.83359 22.0715H10.1113C10.1113 21.4281 10.0234 21.0098 10.4277 21.0098C10.7863 21.0098 10.716 21.3613 10.716 22.0715H11.0043V21.2629H10.9973ZM12.5758 20.7812H12.298V20.9359C12.2031 20.8199 12.0695 20.7461 11.8867 20.7461C11.5246 20.7461 11.2469 21.0344 11.2469 21.4246C11.2469 21.8184 11.5246 22.1031 11.8867 22.1031C12.0695 22.1031 12.2031 22.0363 12.298 21.9133V22.075H12.5758V20.7812ZM13.9996 21.6813C13.9996 21.1539 13.1945 21.393 13.1945 21.1469C13.1945 20.9465 13.6129 20.9781 13.8449 21.1082L13.9609 20.8797C13.6305 20.6652 12.8992 20.6687 12.8992 21.168C12.8992 21.6707 13.7043 21.4598 13.7043 21.6953C13.7043 21.9168 13.2297 21.8992 12.9766 21.7234L12.8535 21.9449C13.2473 22.2121 13.9996 22.1559 13.9996 21.6813ZM15.2441 22.0082L15.1668 21.7691C15.0332 21.843 14.7379 21.9238 14.7379 21.625V21.0414H15.1984V20.7812H14.7379V20.3875H14.4496V20.7812H14.1824V21.0379H14.4496V21.625C14.4496 22.2437 15.0578 22.1312 15.2441 22.0082ZM15.7117 21.5371H16.6785C16.6785 20.9676 16.4184 20.7426 16.0668 20.7426C15.6941 20.7426 15.427 21.0203 15.427 21.4211C15.427 22.1418 16.2215 22.2613 16.6152 21.9203L16.4816 21.7094C16.2074 21.9344 15.7926 21.9133 15.7117 21.5371ZM17.7895 20.7812C17.6277 20.7109 17.3816 20.718 17.2551 20.9359V20.7812H16.9668V22.0715H17.2551V21.3438C17.2551 20.9359 17.5891 20.9887 17.7051 21.0484L17.7895 20.7812ZM18.1621 21.4246C18.1621 21.0238 18.5699 20.8938 18.8898 21.1293L19.0234 20.9008C18.6156 20.5809 17.8738 20.7566 17.8738 21.4281C17.8738 22.1242 18.6613 22.2648 19.0234 21.9555L18.8898 21.727C18.5664 21.9555 18.1621 21.8184 18.1621 21.4246ZM20.507 20.7812H20.2188V20.9359C19.927 20.5492 19.1676 20.7672 19.1676 21.4246C19.1676 22.0996 19.9551 22.293 20.2188 21.9133V22.075H20.507V20.7812ZM21.6918 20.7812C21.6074 20.7391 21.3051 20.6793 21.1574 20.9359V20.7812H20.8797V22.0715H21.1574V21.3438C21.1574 20.957 21.4738 20.9816 21.6074 21.0484L21.6918 20.7812ZM23.1086 20.2574H22.8309V20.9359C22.5426 20.5527 21.7797 20.7566 21.7797 21.4246C21.7797 22.1066 22.5707 22.2895 22.8309 21.9133V22.075H23.1086V20.2574ZM23.3758 17.6172V17.7789H23.4039V17.6172H23.4707V17.5891H23.309V17.6172H23.3758ZM23.6078 21.9695C23.6078 21.952 23.6078 21.9309 23.5973 21.9133C23.5867 21.9027 23.5797 21.8852 23.5691 21.8746C23.5586 21.8641 23.541 21.857 23.5305 21.8465C23.5129 21.8465 23.4918 21.8359 23.4742 21.8359C23.4637 21.8359 23.4461 21.8465 23.425 21.8465C23.4074 21.857 23.3969 21.8641 23.3863 21.8746C23.3687 21.8852 23.3582 21.9027 23.3582 21.9133C23.3477 21.9309 23.3477 21.952 23.3477 21.9695C23.3477 21.9801 23.3477 21.9977 23.3582 22.0188C23.3582 22.0293 23.3687 22.0469 23.3863 22.0574C23.3969 22.068 23.4039 22.075 23.425 22.0855C23.4426 22.0961 23.4637 22.0961 23.4742 22.0961C23.4918 22.0961 23.5129 22.0961 23.5305 22.0855C23.541 22.075 23.5586 22.068 23.5691 22.0574C23.5797 22.0469 23.5867 22.0293 23.5973 22.0188C23.6078 21.9977 23.6078 21.9801 23.6078 21.9695ZM23.7203 17.5855H23.6711L23.6148 17.7086L23.5586 17.5855H23.5094V17.7754H23.5375V17.6312L23.5938 17.7543H23.6324L23.6816 17.6312V17.7754H23.7203V17.5855ZM23.875 14.7555C23.875 12.0766 21.6918 9.89336 19.0059 9.89336C18.0496 9.89336 17.1109 10.1816 16.3164 10.7055C18.8512 12.7902 18.8898 16.7348 16.3164 18.809C17.1109 19.3363 18.0566 19.6211 19.0059 19.6211C21.6918 19.6246 23.875 17.4449 23.875 14.7555Z" fill="white"/>
    </g>
    <defs>
      <clipPath id="clip0_247_12141">
        <path d="M5.875 7H26.125V25H5.875V7Z" fill="white"/>
      </clipPath>
    </defs>
  </svg>
);

// Bank icon configuration interface
interface BankIconConfig {
  icon: React.ReactNode;
  backgroundColor?: string;
  textColor?: string;
  shortName: string;
  hasSvg: boolean;
}

// Bank icon mapping based on issuer names from API and ManualAddCardModal
const bankIconMap: Record<string, BankIconConfig> = {
  // Chase - Has SVG logo
  chase: {
    icon: <ChaseLogo />,
    shortName: "CHASE",
    hasSvg: true,
  },

  // American Express - Has SVG logo
  amex: {
    icon: <AmexLogo />,
    shortName: "AMEX",
    hasSvg: true,
  },

  // Citibank - Text fallback for now
  citi: {
    icon: "CITI",
    backgroundColor: "bg-red-600",
    textColor: "text-white",
    shortName: "CITI",
    hasSvg: false,
  },

  // Bank of America - Text fallback for now
  boa: {
    icon: "BOA",
    backgroundColor: "bg-red-500",
    textColor: "text-white",
    shortName: "BOA",
    hasSvg: false,
  },

  // Default fallback
  default: {
    icon:  <ChaseLogo />,
    backgroundColor: "bg-gray-500",
    textColor: "text-white",
    shortName: "BANK",
    hasSvg: false,
  },
};

/**
 * Get bank icon configuration based on issuer name
 * @param issuer - The issuer name from the API (e.g., "Amex", "Chase", "Citi", "BoA")
 * @returns BankIconConfig object with icon, colors, and short name
 */
export const getBankIcon = (issuer: string): BankIconConfig => {
  if (!issuer) return bankIconMap.default;

  const normalizedIssuer = issuer.toLowerCase().trim();

  // Handle various possible issuer name formats
  if (normalizedIssuer.includes("chase")) {
    return bankIconMap.chase;
  }

  if (normalizedIssuer.includes("amex") || normalizedIssuer.includes("american express")) {
    return bankIconMap.amex;
  }

  if (normalizedIssuer.includes("citi") || normalizedIssuer.includes("citibank")) {
    return bankIconMap.citi;
  }

  if (normalizedIssuer.includes("boa") || normalizedIssuer.includes("bank of america")) {
    return bankIconMap.boa;
  }

  // Return default if no match found
  return bankIconMap.default;
};

/**
 * Bank Icon Component
 * @param issuer - The issuer name from the API
 * @param size - Size class for the icon (default: "w-8 h-8")
 * @param className - Additional CSS classes
 */
interface BankIconProps {
  issuer: string;
  size?: string;
  className?: string;
}

export const BankIcon: React.FC<BankIconProps> = ({
  issuer,
  size = "w-8 h-8",
  className = ""
}) => {
  const iconConfig = getBankIcon(issuer);

  // If it's an SVG logo, render it directly
  if (iconConfig.hasSvg) {
    return (
      <div className={`${size} ${className}`}>
        {iconConfig.icon}
      </div>
    );
  }

  // Otherwise, render text-based icon
  return (
    <div
      className={`${size} ${iconConfig.backgroundColor} ${iconConfig.textColor} rounded-md flex items-center justify-center font-bold text-xs shadow-sm border border-gray-200 ${className}`}
    >
      {iconConfig.icon}
    </div>
  );
};

export default BankIcon;
